/* 友情应用页面样式 */

.friend-apps-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: #999;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 40rpx;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  text-align: center;
}

/* 应用列表 */
.apps-container {
  padding: 0 30rpx 40rpx;
}

/* 页面头部 */
.page-header {
  padding: 40rpx 0 30rpx;
  text-align: center;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 26rpx;
  color: #999;
}

/* 应用网格 */
.apps-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 应用卡片 */
.app-card {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.app-card:active {
  transform: scale(0.98);
  background-color: #f8f8f8;
}

/* 应用图标 */
.app-icon-container {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.app-icon {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.app-icon-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
}

/* 应用信息 */
.app-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.app-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.app-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all;
}

/* 跳转箭头 */
.app-arrow {
  margin-left: 20rpx;
  flex-shrink: 0;
}

.arrow-icon {
  font-size: 36rpx;
  color: #c0c0c0;
  font-weight: 300;
}

/* 底部提示 */
.footer-tip {
  text-align: center;
  margin-top: 40rpx;
  padding: 20rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

/* 响应式设计 */
@media (min-width: 768rpx) {
  .apps-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
  }
  
  .app-card {
    flex-direction: column;
    text-align: center;
    padding: 40rpx 30rpx;
  }
  
  .app-icon-container {
    margin-right: 0;
    margin-bottom: 20rpx;
    width: 100rpx;
    height: 100rpx;
  }
  
  .app-info {
    align-items: center;
  }
  
  .app-name {
    text-align: center;
  }
  
  .app-description {
    text-align: center;
  }
  
  .app-arrow {
    display: none;
  }
}
