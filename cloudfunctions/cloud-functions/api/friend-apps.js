/**
 * 友情应用相关API
 */

const friendAppsDB = require('../db/friend-apps')
const { getCurrentUser, incrementApiCallCount } = require('../utils/auth')
const { success, error, wrapAsync, validateRequired } = require('../utils/response')

/**
 * 获取友情应用列表
 * 只返回可见的友情应用
 */
exports.getFriendApps = wrapAsync(async (params = {}) => {
  // 增加API调用计数
  await incrementApiCallCount(params)

  try {
    const result = await friendAppsDB.getVisibleApps()
    
    if (!result.success) {
      return error(result.errMsg || '获取友情应用列表失败')
    }

    return success({
      apps: result.data,
      total: result.total
    }, '获取友情应用列表成功')
  } catch (err) {
    console.error('获取友情应用列表失败:', err)
    return error('获取友情应用列表失败')
  }
})

/**
 * 创建友情应用（管理员功能）
 * 需要管理员权限
 */
exports.createFriendApp = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['appName', 'navigateParams'])
  if (!validation.success) {
    return validation
  }

  // 获取当前用户信息
  const userResult = await getCurrentUser(params)
  if (!userResult.success) {
    return error('用户验证失败')
  }

  // 这里可以添加管理员权限检查
  // if (!userResult.data.isAdmin) {
  //   return error('权限不足')
  // }

  try {
    const { appName, description, iconUrl, navigateParams, sortOrder } = params

    const appData = {
      appName,
      description: description || '',
      iconUrl: iconUrl || '',
      navigateParams,
      sortOrder: sortOrder || 0
    }

    const result = await friendAppsDB.createApp(appData)
    
    if (!result.success) {
      return error(result.errMsg || '创建友情应用失败')
    }

    return success(result.data, '创建友情应用成功')
  } catch (err) {
    console.error('创建友情应用失败:', err)
    return error('创建友情应用失败')
  }
})

/**
 * 更新友情应用（管理员功能）
 * 需要管理员权限
 */
exports.updateFriendApp = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['id'])
  if (!validation.success) {
    return validation
  }

  // 获取当前用户信息
  const userResult = await getCurrentUser(params)
  if (!userResult.success) {
    return error('用户验证失败')
  }

  // 这里可以添加管理员权限检查
  // if (!userResult.data.isAdmin) {
  //   return error('权限不足')
  // }

  try {
    const { id, appName, description, iconUrl, navigateParams, sortOrder, isVisible } = params

    const updateData = {}
    if (appName !== undefined) updateData.appName = appName
    if (description !== undefined) updateData.description = description
    if (iconUrl !== undefined) updateData.iconUrl = iconUrl
    if (navigateParams !== undefined) updateData.navigateParams = navigateParams
    if (sortOrder !== undefined) updateData.sortOrder = sortOrder
    if (isVisible !== undefined) updateData.isVisible = isVisible

    const result = await friendAppsDB.updateApp(id, updateData)
    
    if (!result.success) {
      return error(result.errMsg || '更新友情应用失败')
    }

    return success(result.data, '更新友情应用成功')
  } catch (err) {
    console.error('更新友情应用失败:', err)
    return error('更新友情应用失败')
  }
})

/**
 * 删除友情应用（管理员功能）
 * 需要管理员权限
 */
exports.deleteFriendApp = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['id'])
  if (!validation.success) {
    return validation
  }

  // 获取当前用户信息
  const userResult = await getCurrentUser(params)
  if (!userResult.success) {
    return error('用户验证失败')
  }

  // 这里可以添加管理员权限检查
  // if (!userResult.data.isAdmin) {
  //   return error('权限不足')
  // }

  try {
    const { id } = params

    const result = await friendAppsDB.deleteApp(id)
    
    if (!result.success) {
      return error(result.errMsg || '删除友情应用失败')
    }

    return success(result.data, '删除友情应用成功')
  } catch (err) {
    console.error('删除友情应用失败:', err)
    return error('删除友情应用失败')
  }
})

/**
 * 切换友情应用可见性（管理员功能）
 * 需要管理员权限
 */
exports.toggleFriendAppVisibility = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['id', 'isVisible'])
  if (!validation.success) {
    return validation
  }

  // 获取当前用户信息
  const userResult = await getCurrentUser(params)
  if (!userResult.success) {
    return error('用户验证失败')
  }

  // 这里可以添加管理员权限检查
  // if (!userResult.data.isAdmin) {
  //   return error('权限不足')
  // }

  try {
    const { id, isVisible } = params

    const result = await friendAppsDB.toggleVisibility(id, isVisible)
    
    if (!result.success) {
      return error(result.errMsg || '切换应用可见性失败')
    }

    return success(result.data, '切换应用可见性成功')
  } catch (err) {
    console.error('切换应用可见性失败:', err)
    return error('切换应用可见性失败')
  }
})

/**
 * 获取友情应用统计信息（管理员功能）
 * 需要管理员权限
 */
exports.getFriendAppStats = wrapAsync(async (params = {}) => {
  // 获取当前用户信息
  const userResult = await getCurrentUser(params)
  if (!userResult.success) {
    return error('用户验证失败')
  }

  // 这里可以添加管理员权限检查
  // if (!userResult.data.isAdmin) {
  //   return error('权限不足')
  // }

  try {
    const result = await friendAppsDB.getStats()
    
    if (!result.success) {
      return error(result.errMsg || '获取统计信息失败')
    }

    return success(result.data, '获取统计信息成功')
  } catch (err) {
    console.error('获取友情应用统计失败:', err)
    return error('获取友情应用统计失败')
  }
})
